import { PagingStrategies } from '@nestjs-query/query-graphql';
import { registerEnumType } from '@nestjs/graphql';

export const IS_PRODUCTION_ENV = process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'staging';

export const defaultQueryOptions = {
  enableTotalCount: true,
  pagingStrategy: PagingStrategies.OFFSET,
  maxResultsSize: -1
};

export const ACCESS_TOKEN_API_NAME = 'Access Token';

export enum RoleTypeEnum {
  Admin = 'Admin',
  User = 'User'
}
registerEnumType(RoleTypeEnum, { name: 'RoleTypeEnum' });

export enum ProjectUserRoleType {
  CanEdit = 'CanEdit',
  CanView = 'CanView',
  ProjectOwner = 'ProjectOwner',
  CloudCoordinator = 'CloudCoordinator'
}
registerEnumType(ProjectUserRoleType, { name: 'ProjectUserRoleType' });

export enum ProjectDocumentPermissionType {
  CanView = 'CanView',
  CanDownload = 'CanDownload',
  CanEdit = 'CanEdit',
  CanDelete = 'CanDelete',
  CanShare = 'CanShare'
}
registerEnumType(ProjectDocumentPermissionType, { name: 'ProjectDocumentPermissionType' });

// Project and Task Status
export enum ProjectStatusType {
  InProgress = 'InProgress',
  Completed = 'Completed',
  Overdue = 'Overdue'
}
registerEnumType(ProjectStatusType, { name: 'ProjectStatusType' });

export enum TaskStatusType {
  Open = 'Open',
  InProgress = 'InProgress',
  Hold = 'Hold',
  Completed = 'Completed',
  Overdue = 'Overdue'
}
registerEnumType(TaskStatusType, { name: 'TaskStatusType' });

// Enum for Audit Log
export enum AuditLogModuleType {
  Project = 'Project',
  Task = 'Task',
  TaskComment = 'TaskComment',
  // DocumentComment   = 'DocumentComment',
  Photo = 'Photo',
  Drawing = 'Drawing',
  Workspace = 'Workspace',
  WorkspaceComment = 'WorkspaceComment',
  Calendar = 'Calendar',
  SCurve = 'SCurve',
  Overview = 'Overview',
  CloudDocument = 'CloudDocument',
  Schedule = 'Schedule',
  Member = 'Member',
  ProjectGroup = 'ProjectGroup',
  WorkspaceGroup = 'WorkspaceGroup',
  DocumentEditor = 'DocumentEditor'
}
registerEnumType(AuditLogModuleType, { name: 'AuditLogModuleType' });

export enum AuditLogActionType {
  Add = 'Add',
  AddRole = 'AddRole',
  AddNote = 'AddNote',
  DeleteNote = 'DeleteNote',
  AddMarkup = 'AddMarkup',
  AddAttachment = 'AddAttachment',
  RemoveAttachment = 'RemoveAttachment',
  LinkedDocument = 'LinkedDocument',
  UnlinkedDocument = 'UnlinkedDocument',
  AddPhoto = 'AddPhoto',
  RemovePhoto = 'RemovePhoto',
  Create = 'Create',
  Update = 'Update',
  Delete = 'Delete',
  Assigned = 'Assigned',
  Unassigned = 'Unassigned',
  AssignedCc = 'Assigned Cc',
  UnassignedCc = 'Unassigned Cc',
  RequestApproval = 'RequestApproval',
  UpdateStatus = 'UpdateStatus',
  UpdateGroup = 'UpdateGroup',
  Invited = 'Invited',
  Joined = 'Joined',
  Shared = 'Shared',
  AddProposed = 'AddProposed',
  ValidateProposed = 'ValidateProposed',
  RejectProposed = 'RejectProposed'
}
registerEnumType(AuditLogActionType, { name: 'AuditLogActionType' });

// Enum for Project Document
export enum CategoryType {
  ProjectDocument = 'ProjectDocument',
  WorkProgramme = 'WorkProgramme',
  Correspondence = 'Correspondence',
  AllForm = 'AllForm',
  StandardForm = 'StandardForm',
  Photo = 'Photo',
  TwoDDrawings = 'TwoDDrawings',
  BIMDrawings = 'BIMDrawings',
  SCurveGraph = 'SCurveGraph',
  GanttChart = 'GanttChart'
}
registerEnumType(CategoryType, { name: 'CategoryType' });

// Enum for Drawing Revision
export enum DrawingStatus {
  Obsolete = 'Obsolete',
  Revision = 'Revision'
}
registerEnumType(DrawingStatus, { name: 'DrawingStatus' });

export enum CorrespondenceType {
  CorrespondenceIn = 'Correspondence In',
  CorrespondenceOut = 'Correspondence Out'
}

export enum FileSystemType {
  Document = 'Document',
  Folder = 'Folder'
}
registerEnumType(FileSystemType, { name: 'FileSystemType' });

export enum FileChannelTypes {
  OBS = 'OBS',
  FORGE = 'FORGE'
}
registerEnumType(FileChannelTypes, { name: 'FileChannelTypes' });

export enum ProjectDocumentStatus {
  Pending = 'Pending',
  Draft = 'Draft',
  Submitted = 'Submitted',
  InReview = 'InReview',
  Rejected = 'Rejected',
  Approved = 'Approved',
  InProgress = 'InProgress',
  Amend = 'Amend',
}
registerEnumType(ProjectDocumentStatus, { name: 'ProjectDocumentStatus' });

export enum RequestForSignatureStatus {
  Pending = 'Pending',
  Unsent = 'Unsent',
  Sent = 'Sent',
  InReview = 'InReview',
  Rejected = 'Rejected',
  Proceed = 'Proceed',
  Approved = 'Approved',
  Amend = 'Amend',
  InProgress = 'InProgress'
}
registerEnumType(RequestForSignatureStatus, { name: 'RequestForSignatureStatus' });

export enum ProjectDocumentUserPermissionType {
  Include = 'Include',
  Exclude = 'Exclude'
}
registerEnumType(ProjectDocumentUserPermissionType, { name: 'ProjectDocumentUserPermissionType' });
export enum WorkspaceCCStatus {
  Unsent = 'Unsent',
  Sent = 'Sent'
}
registerEnumType(WorkspaceCCStatus, { name: 'WorkspaceCCStatus' });

export enum ProjectDocumentDriveType {
  Shared = 'Shared',
  Personal = 'Personal'
}
registerEnumType(ProjectDocumentDriveType, { name: 'ProjectDocumentDriveType' });

export enum ScheduleStatus {
  Pending = 'Pending',
  Upcoming = 'Upcoming',
  InProgress = 'InProgress',
  Hold = 'Hold',
  Completed = 'Completed',
  Delay = 'Delay',
  StartDelay = 'StartDelay',
  FinishDelay = 'FinishDelay'
}
registerEnumType(ScheduleStatus, { name: 'ScheduleStatus' });

export enum ScheduleType {
  Task = 'task',
  Milestone = 'milestone',
  Project = 'project'
}
registerEnumType(ScheduleType, { name: 'ScheduleType' });

export enum ScheduleRespondStatus {
  Validated = 'Validated',
  Rejected = 'Rejected'
}
registerEnumType(ScheduleRespondStatus, { name: 'ScheduleRespondStatus' });

export enum ScheduleUserRole {
  Manager = 'Manager',
  Validator = 'Validator',
  Collaborator = 'Collaborator'
}
registerEnumType(ScheduleUserRole, { name: 'ScheduleUserRole' });

export enum ProposeAction {
  Validate = 'Validate',
  Reject = 'Reject'
}
registerEnumType(ProposeAction, { name: 'ProposeAction' });

export enum workflowType {
  Linear = 'Linear',
  Dynamic = 'Dynamic',
}
registerEnumType(workflowType, { name: 'workflowType' });

export enum LanguageType {
  BahasaMelayu = 'BahasaMelayu',
  English = 'English'
}
registerEnumType(LanguageType, { name: 'LanguageType' });

export enum SourceType {
  OfflineApp = 'OfflineApp',
  Web = 'Web',
}
registerEnumType(SourceType, { name: 'SourceType' });

export enum EmailAssetType {
  Inline = 'Inline',
  Attachment = 'Attachment'
}
registerEnumType(EmailAssetType, { name: 'EmailAssetType' });

export enum SalesOrderStatus {
  Pending = 'pending',
  Paid = 'paid',
  Declined = 'declined',
  InProgress = 'inprogress',
  Cancelled = 'cancelled',
  Deferred = 'deferred',
  PaymentDue = 'payment_due',
  Overdue = 'overdue'
}
registerEnumType(SalesOrderStatus, { name: 'SalesOrderStatus' });

export enum SubscriptionStatus {
  Active = 'active',
  PaymentDue = 'payment_due',
  Suspended = 'suspended',
  Cancelled = 'cancelled',
  Expired = 'expired'
}
registerEnumType(SubscriptionStatus, { name: 'SubscriptionStatus' });
