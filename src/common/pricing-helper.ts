import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';
import { YEARLY_DISCOUNT_RATE } from '@constants/pricing.constants';
import moment from 'moment';
import { ComprehensivePricingCalculation, MemberPricing, PaymentSchedule, PricingPeriod, SavingsInfo, SimplifiedPricingDisplay, PeriodCostBreakdown } from '../types/pricing.types';

/**
 * Interface for subscription price calculation result
 */
export interface SubscriptionPriceResult {
  // First period (prorated or initial month)
  firstPeriod: {
    startDate: Date;
    endDate: Date;
    baseAmountInCents: number;
    sstAmountInCents: number;
    totalAmountInCents: number;
    baseAmount: number;
    sstAmount: number;
    totalAmount: number;
    isProrated: boolean;
    label: string;
    formattedStartDate: string;
    formattedEndDate: string;
  };
  // Full month period
  fullMonthPeriod: {
    startDate: Date;
    endDate: Date;
    baseAmountInCents: number;
    sstAmountInCents: number;
    totalAmountInCents: number;
    baseAmount: number;
    sstAmount: number;
    totalAmount: number;
    formattedStartDate: string;
    formattedEndDate: string;
  };
  // Combined amounts
  combined: {
    baseAmountInCents: number;
    sstAmountInCents: number;
    totalAmountInCents: number;
    baseAmount: number;
    sstAmount: number;
    totalAmount: number;
  };
  // Subscription details
  subscriptionEndDate: Date;
  formattedSubscriptionEndDate: string;
  // Billing scenario details
  isFirstDayOfMonth: boolean;
  isAfterMidMonth: boolean;
  dayOfMonth: number;
}

/**
 * Calculate a single period price (prorated or full)
 * @param monthlyTotal The total monthly cost for all members
 * @param isProrated Whether to calculate prorated amount
 * @param isYearly Whether the billing is yearly
 * @param startDate The start date of the period
 * @param endDate The end date of the period
 * @returns The calculated price in cents
 */
const calculatePeriodPrice = (
  monthlyTotal: number,
  isProrated: boolean,
  isYearly: boolean,
  startDate: moment.Moment,
  endDate: moment.Moment
): { baseAmountInCents: number; sstAmountInCents: number; totalAmountInCents: number } => {
  let total = monthlyTotal;

  if (isProrated) {
    // Calculate days according to billing flow documentation
    const firstDayOfMonth = moment(startDate).date(1);
    const daysTotal = endDate.diff(firstDayOfMonth, 'days') + 1;

    // Calculate days remaining
    const daysRemaining = endDate.diff(startDate, 'days');

    // Calculate prorated amount
    total = (monthlyTotal / daysTotal) * daysRemaining;
    total = Math.round(total * 100) / 100;
  } else if (isYearly) {
    // For yearly billing, charge the full yearly amount upfront (12 months)
    total = monthlyTotal * 12;
  }

  // Convert base amount to cents
  const baseAmountInCents = Math.round(total * 100);

  // Calculate SST amount (6%)
  const sstAmountInCents = Math.round(baseAmountInCents * 0.06);

  // Calculate total amount including SST
  const totalAmountInCents = baseAmountInCents + sstAmountInCents;

  return {
    baseAmountInCents,
    sstAmountInCents,
    totalAmountInCents
  };
};

/**
 * Calculate the complete subscription price including prorated and full month periods
 * @param subscriptionPackage The subscription package
 * @param teamSize Number of team members
 * @param isYearly Whether the billing is yearly
 * @param billingDate Date when billing starts (defaults to current date)
 * @returns A structured response with all pricing details
 */
export const calculateSubscriptionPrice = (
  subscriptionPackage: SubscriptionPackageEntity,
  teamSize: number,
  isYearly: boolean,
  billingDate: Date = new Date()
): SubscriptionPriceResult => {
  if (!subscriptionPackage) {
    throw new Error('Subscription package is required');
  }

  // Ensure valid inputs
  const validTeamSize = Math.max(1, teamSize || 1); // Minimum 1 user
  const validIsYearly = Boolean(isYearly);

  // Base monthly price per member (amount is already price per member)
  const monthlyPrice = subscriptionPackage.amount;

  // Discounted monthly price per member (if yearly billing)
  const discountedMonthlyPrice = validIsYearly
    ? Math.round(monthlyPrice * (1 - YEARLY_DISCOUNT_RATE))
    : monthlyPrice;

  // Total monthly cost for all members
  const monthlyTotal = discountedMonthlyPrice * validTeamSize;

  // Calculate dates
  const currentDate = moment(billingDate);
  const currentAnchorDate = moment(billingDate).date(27);
  if (currentDate.date() > 27) {
    currentAnchorDate.add(1, 'month');
  }
  const nextMonthAnchorDate = moment(currentAnchorDate).add(1, 'month');

  // For yearly subscriptions, calculate the end date as 12 months from the current anchor date
  const subscriptionEndDate = validIsYearly
    ? moment(currentAnchorDate).add(12, 'months')
    : nextMonthAnchorDate;

  // Determine billing scenarios
  const isFirstDayOfMonth = currentDate.date() === 1;
  const isAfterMidMonth = currentDate.date() > 14;
  const dayOfMonth = currentDate.date();

  // Determine if proration should be applied for the first period
  // - Yearly subscriptions: NEVER prorate (always charge full yearly amount)
  // - Monthly subscriptions: Prorate for any date that's not the 1st of the month
  // - No proration only for 1st day of month (charge full month until next 27th)
  // - Proration for all other dates (2nd through 26th)
  // - Dates after 27th are handled as next billing cycle
  const shouldProrate = validIsYearly ? false : (dayOfMonth > 1 && dayOfMonth < 27);

  // Calculate first period amount (prorated or initial month)
  const firstPeriodResult = calculatePeriodPrice(
    monthlyTotal,
    shouldProrate,
    validIsYearly,
    currentDate,
    currentAnchorDate
  );

  // Calculate full month amount (only for prorated subscriptions)
  let fullMonthResult: { baseAmountInCents: number; sstAmountInCents: number; totalAmountInCents: number };
  let combinedBaseAmountInCents: number;
  let combinedSstAmountInCents: number;
  let combinedTotalAmountInCents: number;

  if (shouldProrate) {
    // For prorated subscriptions: only charge for the current billing period (until next 27th)
    // No additional full month charge - customer will be billed again on the 27th
    fullMonthResult = {
      baseAmountInCents: 0,
      sstAmountInCents: 0,
      totalAmountInCents: 0
    };

    combinedBaseAmountInCents = firstPeriodResult.baseAmountInCents;
    combinedSstAmountInCents = firstPeriodResult.sstAmountInCents;
    combinedTotalAmountInCents = firstPeriodResult.totalAmountInCents;
  } else {
    // For non-prorated subscriptions: only the first period (until next anchor date)
    fullMonthResult = {
      baseAmountInCents: 0,
      sstAmountInCents: 0,
      totalAmountInCents: 0
    };

    combinedBaseAmountInCents = firstPeriodResult.baseAmountInCents;
    combinedSstAmountInCents = firstPeriodResult.sstAmountInCents;
    combinedTotalAmountInCents = firstPeriodResult.totalAmountInCents;
  }

  // Convert amounts to RM for display
  const firstPeriodBaseAmount = +(firstPeriodResult.baseAmountInCents / 100).toFixed(2);
  const firstPeriodSstAmount = +(firstPeriodResult.sstAmountInCents / 100).toFixed(2);
  const firstPeriodTotalAmount = +(firstPeriodResult.totalAmountInCents / 100).toFixed(2);

  const fullMonthBaseAmount = +(fullMonthResult.baseAmountInCents / 100).toFixed(2);
  const fullMonthSstAmount = +(fullMonthResult.sstAmountInCents / 100).toFixed(2);
  const fullMonthTotalAmount = +(fullMonthResult.totalAmountInCents / 100).toFixed(2);

  const combinedBaseAmount = +(combinedBaseAmountInCents / 100).toFixed(2);
  const combinedSstAmount = +(combinedSstAmountInCents / 100).toFixed(2);
  const combinedTotalAmount = +(combinedTotalAmountInCents / 100).toFixed(2);

  // Determine the label for the first period
  const firstPeriodLabel = validIsYearly ? "Annual" : (shouldProrate ? "Prorated period" : "Initial month");

  // Format dates for display
  const formatDate = (date: moment.Moment): string => date.format('YYYY-MM-DD');
  const formatDisplayDate = (date: moment.Moment): string => date.format('D MMM');
  const formatDisplayDateWithYear = (date: moment.Moment): string => date.format('D MMM YYYY');

  return {
    firstPeriod: {
      startDate: currentDate.toDate(),
      endDate: validIsYearly ? subscriptionEndDate.toDate() : currentAnchorDate.toDate(),
      baseAmountInCents: firstPeriodResult.baseAmountInCents,
      sstAmountInCents: firstPeriodResult.sstAmountInCents,
      totalAmountInCents: firstPeriodResult.totalAmountInCents,
      baseAmount: firstPeriodBaseAmount,
      sstAmount: firstPeriodSstAmount,
      totalAmount: firstPeriodTotalAmount,
      isProrated: shouldProrate,
      label: firstPeriodLabel,
      formattedStartDate: formatDisplayDate(currentDate),
      formattedEndDate: validIsYearly
        ? formatDisplayDateWithYear(subscriptionEndDate)
        : formatDisplayDate(currentAnchorDate)
    },
    fullMonthPeriod: {
      startDate: currentAnchorDate.toDate(),
      endDate: nextMonthAnchorDate.toDate(),
      baseAmountInCents: fullMonthResult.baseAmountInCents,
      sstAmountInCents: fullMonthResult.sstAmountInCents,
      totalAmountInCents: fullMonthResult.totalAmountInCents,
      baseAmount: fullMonthBaseAmount,
      sstAmount: fullMonthSstAmount,
      totalAmount: fullMonthTotalAmount,
      formattedStartDate: formatDisplayDate(currentAnchorDate),
      formattedEndDate: formatDisplayDate(nextMonthAnchorDate)
    },
    combined: {
      baseAmountInCents: combinedBaseAmountInCents,
      sstAmountInCents: combinedSstAmountInCents,
      totalAmountInCents: combinedTotalAmountInCents,
      baseAmount: combinedBaseAmount,
      sstAmount: combinedSstAmount,
      totalAmount: combinedTotalAmount
    },
    subscriptionEndDate: subscriptionEndDate.toDate(),
    formattedSubscriptionEndDate: formatDate(subscriptionEndDate),
    isFirstDayOfMonth,
    isAfterMidMonth,
    dayOfMonth
  };
};

/**
 * Calculate comprehensive pricing information using the new enhanced types
 * @param subscriptionPackage The subscription package
 * @param teamSize Number of team members
 * @param isYearly Whether the billing is yearly
 * @param billingDate Date when billing starts (defaults to current date)
 * @param calculationType Type of calculation being performed
 * @param currentTeamSize Current team size (for member changes)
 * @param existingSubscriptionEndDate Existing subscription end date (for member changes)
 * @returns Comprehensive pricing calculation result
 */
export const calculateComprehensivePricing = (
  subscriptionPackage: SubscriptionPackageEntity,
  teamSize: number,
  isYearly: boolean,
  billingDate: Date = new Date(),
  calculationType: 'new' | 'upgrade' | 'member_change' | 'renewal' = 'new',
  currentTeamSize?: number,
  existingSubscriptionEndDate?: Date
): ComprehensivePricingCalculation => {
  if (!subscriptionPackage) {
    throw new Error('Subscription package is required');
  }

  // Ensure valid inputs
  const validTeamSize = Math.max(1, teamSize || 1);
  const validIsYearly = Boolean(isYearly);
  const validCurrentTeamSize = currentTeamSize || validTeamSize;

  // Base monthly price per member
  const originalPricePerMember = subscriptionPackage.amount;

  // Discounted monthly price per member (if yearly billing)
  const discountedPricePerMember = validIsYearly
    ? Math.round(originalPricePerMember * (1 - YEARLY_DISCOUNT_RATE))
    : originalPricePerMember;

  // Effective monthly price (same as discounted for now)
  const effectiveMonthlyPricePerMember = discountedPricePerMember;

  // Total monthly cost for all members
  const monthlyTotal = discountedPricePerMember * validTeamSize;

  // Calculate dates
  const currentDate = moment(billingDate);
  const currentAnchorDate = moment(billingDate).date(27);
  if (currentDate.date() > 27) {
    currentAnchorDate.add(1, 'month');
  }
  const nextMonthAnchorDate = moment(currentAnchorDate).add(1, 'month');

  // For member changes, use the existing subscription end date
  // For new subscriptions, calculate based on billing type
  let subscriptionEndDate: moment.Moment;
  if (calculationType === 'member_change' && existingSubscriptionEndDate) {
    subscriptionEndDate = moment(existingSubscriptionEndDate);
  } else if (validIsYearly) {
    // For yearly subscriptions, calculate the end date as 12 months from the current anchor date
    subscriptionEndDate = moment(currentAnchorDate).add(12, 'months');
  } else {
    // For monthly subscriptions, end date is the next anchor date
    subscriptionEndDate = nextMonthAnchorDate;
  }

  // Determine billing scenarios
  const dayOfMonth = currentDate.date();
  let shouldProrate = dayOfMonth > 14 && dayOfMonth < 27;
  let currentPeriodCosts: PeriodCostBreakdown;
  let nextPeriodCosts: PeriodCostBreakdown;

  if (calculationType === 'member_change') {
    // For member changes, calculate cost from today until the next billing cycle (27th)
    // This ensures we only charge for the current billing period, not the entire subscription
    currentPeriodCosts = calculateMemberChangeCosts(
      monthlyTotal,
      currentDate,
      currentAnchorDate // Use next anchor date instead of subscription end date
    );

    // No next period costs for member changes (all calculated in current period)
    nextPeriodCosts = { baseAmount: 0, discountAmount: 0, subtotalAmount: 0, taxAmount: 0, totalAmount: 0, taxRate: 0.06, discountRate: 0 };

    // Member changes are always considered prorated since they're calculated from today to anchor date
    shouldProrate = true;
  } else {
    // Updated logic for new subscriptions
    // - Yearly subscriptions: NEVER prorate (always charge full yearly amount)
    // - Monthly subscriptions: Prorate for any date except 1st
    shouldProrate = validIsYearly ? false : (dayOfMonth > 1 && dayOfMonth < 27);

    // Calculate current period costs
    currentPeriodCosts = calculatePeriodCosts(
      monthlyTotal,
      shouldProrate,
      validIsYearly,
      currentDate,
      currentAnchorDate
    );

    // For new subscriptions, don't charge for next period - customer will be billed on the 27th
    // This ensures we only charge for the current billing period
    nextPeriodCosts = { baseAmount: 0, discountAmount: 0, subtotalAmount: 0, taxAmount: 0, totalAmount: 0, taxRate: 0.06, discountRate: 0 };
  }

  // Calculate combined totals
  const combinedTotals: PeriodCostBreakdown = {
    baseAmount: currentPeriodCosts.baseAmount + nextPeriodCosts.baseAmount,
    discountAmount: currentPeriodCosts.discountAmount + nextPeriodCosts.discountAmount,
    subtotalAmount: currentPeriodCosts.subtotalAmount + nextPeriodCosts.subtotalAmount,
    taxAmount: currentPeriodCosts.taxAmount + nextPeriodCosts.taxAmount,
    totalAmount: currentPeriodCosts.totalAmount + nextPeriodCosts.totalAmount,
    taxRate: 0.06,
    discountRate: validIsYearly ? YEARLY_DISCOUNT_RATE : 0
  };

  // Build member pricing
  const memberPricing: MemberPricing = {
    originalPricePerMember,
    discountedPricePerMember,
    effectiveMonthlyPricePerMember,
    currentTeamSize: validCurrentTeamSize,
    newTeamSize: calculationType === 'member_change' ? (validCurrentTeamSize + validTeamSize) : undefined
  };

  // Build current period
  const currentPeriod: PricingPeriod = {
    startDate: formatDisplayDate(currentDate),
    endDate: calculationType === 'member_change'
      ? formatDisplayDate(currentAnchorDate) // For member changes, show until next billing cycle
      : (validIsYearly
        ? formatDisplayDateWithYear(subscriptionEndDate)
        : formatDisplayDate(currentAnchorDate)),
    description: calculationType === 'member_change'
      ? "Until Next Billing Cycle"
      : (shouldProrate ? "Partial Month" : (validIsYearly ? "Annual" : "Full Month")),
    isProrated: shouldProrate,
    costs: currentPeriodCosts
  };

  // Build next period
  const nextPeriod: PricingPeriod = {
    startDate: formatDisplayDate(currentAnchorDate),
    endDate: formatDisplayDate(nextMonthAnchorDate),
    description: "Full Month",
    isProrated: false,
    costs: nextPeriodCosts
  };

  // Build savings info (for yearly subscriptions)
  let savings: SavingsInfo | undefined;
  if (validIsYearly) {
    const monthlyBillingTotal = originalPricePerMember * validTeamSize * 12;
    const yearlyBillingTotal = discountedPricePerMember * validTeamSize * 12;
    const annualSavings = monthlyBillingTotal - yearlyBillingTotal;

    savings = {
      monthlyBillingTotal,
      yearlyBillingTotal,
      annualSavings,
      savingsPercentage: YEARLY_DISCOUNT_RATE * 100,
      monthlySavings: annualSavings / 12
    };
  }

  // Build payment schedule
  let ongoingMonthlyAmount: number | undefined;
  let previousMonthlyAmount: number | undefined;

  if (calculationType === 'member_change' && currentTeamSize) {
    // For member changes, calculate the ongoing monthly amount for the new team size
    const newTeamSize = validCurrentTeamSize + validTeamSize; // Current + additional members
    ongoingMonthlyAmount = Math.round((discountedPricePerMember * newTeamSize * 1.06) * 100) / 100; // Include SST
    previousMonthlyAmount = Math.round((discountedPricePerMember * validCurrentTeamSize * 1.06) * 100) / 100; // Include SST
  }

  const paymentSchedule: PaymentSchedule = {
    nextPaymentDate: calculationType === 'member_change'
      ? formatDate(currentAnchorDate) // For member changes, next payment is at the next billing cycle
      : formatDate(subscriptionEndDate),
    nextPaymentAmount: validIsYearly ? combinedTotals.totalAmount : (ongoingMonthlyAmount || monthlyTotal),
    billingFrequency: validIsYearly ? 'yearly' : 'monthly',
    subscriptionEndDate: formatDate(subscriptionEndDate),
    daysUntilNextPayment: calculationType === 'member_change'
      ? currentAnchorDate.diff(currentDate, 'days') // Days until next billing cycle
      : subscriptionEndDate.diff(currentDate, 'days'),
    ongoingMonthlyAmount,
    previousMonthlyAmount
  };

  return {
    subscription: {
      packageId: subscriptionPackage.id.toString(),
      packageName: subscriptionPackage.title,
      isYearly: validIsYearly,
      calculationType
    },
    memberPricing,
    periods: {
      currentPeriod,
      nextPeriod,
      combinedTotals
    },
    savings,
    paymentSchedule,
    context: {
      currency: 'MYR',
      timezone: 'Asia/Kuala_Lumpur',
      calculatedAt: moment().toISOString()
    }
  };
};

/**
 * Helper function to calculate period costs with proper breakdown
 */
const calculatePeriodCosts = (
  monthlyTotal: number,
  isProrated: boolean,
  isYearly: boolean,
  startDate: moment.Moment,
  endDate: moment.Moment
): PeriodCostBreakdown => {
  let baseAmount = monthlyTotal;

  if (isProrated) {
    // Calculate prorated amount
    const firstDayOfMonth = moment(startDate).date(1);
    const daysTotal = endDate.diff(firstDayOfMonth, 'days') + 1;
    const daysRemaining = endDate.diff(startDate, 'days');
    baseAmount = (monthlyTotal / daysTotal) * daysRemaining;
    baseAmount = Math.round(baseAmount * 100) / 100;
  } else if (isYearly) {
    // For yearly billing, charge the full yearly amount upfront (12 months)
    baseAmount = monthlyTotal * 12;
  }

  // No additional discounts applied at period level (yearly discount already applied to monthly total)
  const discountAmount = 0;
  const subtotalAmount = baseAmount - discountAmount;

  // Calculate SST (6%)
  const taxRate = 0.06;
  const taxAmount = Math.round(subtotalAmount * taxRate * 100) / 100;

  // Calculate total
  const totalAmount = subtotalAmount + taxAmount;

  return {
    baseAmount: Math.round(baseAmount * 100) / 100,
    discountAmount,
    subtotalAmount: Math.round(subtotalAmount * 100) / 100,
    taxAmount,
    totalAmount: Math.round(totalAmount * 100) / 100,
    taxRate,
    discountRate: 0
  };
};

/**
 * Helper function to calculate member change costs from current date to subscription end date
 * Uses the same proration logic as regular subscriptions for consistency
 */
const calculateMemberChangeCosts = (
  monthlyTotal: number,
  startDate: moment.Moment,
  endDate: moment.Moment
): PeriodCostBreakdown => {
  // For member changes, we need to calculate the prorated amount from today to the next billing cycle (27th)
  // This should follow the same logic as regular proration

  // Find the next anchor date (27th of current or next month)
  const currentAnchorDate = moment(startDate).date(27);
  if (startDate.date() > 27) {
    currentAnchorDate.add(1, 'month');
  }

  // Calculate prorated amount from start date to the next anchor date (27th)
  const firstDayOfMonth = moment(startDate).date(1);
  const daysInMonth = currentAnchorDate.diff(firstDayOfMonth, 'days') + 1;
  const daysRemaining = currentAnchorDate.diff(startDate, 'days');

  // Calculate base amount using proper proration
  let baseAmount = (monthlyTotal / daysInMonth) * daysRemaining;
  baseAmount = Math.round(baseAmount * 100) / 100;

  // No additional discounts applied at period level
  const discountAmount = 0;
  const subtotalAmount = baseAmount - discountAmount;

  // Calculate SST (6%)
  const taxRate = 0.06;
  const taxAmount = Math.round(subtotalAmount * taxRate * 100) / 100;

  // Calculate total
  const totalAmount = subtotalAmount + taxAmount;

  return {
    baseAmount: Math.round(baseAmount * 100) / 100,
    discountAmount,
    subtotalAmount: Math.round(subtotalAmount * 100) / 100,
    taxAmount,
    totalAmount: Math.round(totalAmount * 100) / 100,
    taxRate,
    discountRate: 0
  };
};

/**
 * Format dates for display
 */
const formatDate = (date: moment.Moment): string => date.format('YYYY-MM-DD');
const formatDisplayDate = (date: moment.Moment): string => date.format('D MMM');
const formatDisplayDateWithYear = (date: moment.Moment): string => date.format('D MMM YYYY');

/**
 * Generate simplified pricing display data from comprehensive calculation
 */
export const generateSimplifiedPricingDisplay = (
  comprehensiveResult: ComprehensivePricingCalculation
): SimplifiedPricingDisplay => {
  const { memberPricing, periods, savings } = comprehensiveResult;

  return {
    pricePerMember: `RM ${memberPricing.effectiveMonthlyPricePerMember.toFixed(2)}`,
    totalMonthly: `RM ${(memberPricing.effectiveMonthlyPricePerMember * memberPricing.currentTeamSize).toFixed(2)}`,
    totalForPeriod: `RM ${periods.combinedTotals.totalAmount.toFixed(2)}`,
    renewalDate: comprehensiveResult.paymentSchedule.subscriptionEndDate,
    savingsText: savings ? `Save RM ${savings.annualSavings.toFixed(2)} annually` : undefined,
    includesProration: periods.currentPeriod.isProrated
  };
};