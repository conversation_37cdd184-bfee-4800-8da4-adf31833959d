import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { SubscriptionPackageEntity } from './entity/subscription-package.entity';
import { Repository, getRepository, MoreThan } from 'typeorm';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { RoleTypeEnum } from '@constants';
import { CompanySubscriptionEntity } from '@modules/company-subscription/entity/company-subscription.entity';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { SalesOrderEntity } from '@modules/sales-order/entity/sales-order.entity';
import { getErrorMessage } from '@common/error';
import { calculateSubscriptionPrice, SubscriptionPriceResult, calculateComprehensivePricing } from '@common/pricing-helper';
import { ComprehensivePricingCalculation } from '../../types/pricing.types';
import { YEARLY_DISCOUNT_RATE } from '@constants/pricing.constants';
import {
  PricingCalculationInputDto,
  PricingCalculationResultDto,
  PricingCalculationType,
  PricingDetailsDto,
  CurrentPeriodProratedDto,
  NextPeriodDto,
  YearlySavingsDto,
  PricingBreakdownDto
} from './dto/subscription-package.gql.dto';
import moment from 'moment';

// Define interfaces for order data structure
interface BillingPeriod {
  startDate: string;
  endDate: string;
  amount: number;
  baseAmount: number;
  sstAmount: number;
  isProrated?: boolean;
  label?: string;
  formattedStartDate?: string;
  formattedEndDate?: string;
}

interface BillingDetails {
  firstPeriod: BillingPeriod;
  fullMonthPeriod: BillingPeriod;
  combinedAmount: number;
  combinedBaseAmount: number;
  combinedSstAmount: number;
  subscriptionEndDate: string;
}

interface OrderData {
  // Seat addition properties
  prorationType?: 'seat-addition';
  newSeatCount?: number;

  // Renewal properties
  billingType?: 'renewal';

  // Subscription properties
  teamSize?: number;
  isYearly?: boolean;
  originalAmount?: number;
  fullDescription?: string;

  // Billing calculation details
  billingDetails?: BillingDetails;

  // Subscription activation tracking
  subscriptionActivated?: boolean;
  activatedBy?: string;
  activatedAt?: string;
  subscriptionId?: number;

  // Payment details
  payment?: {
    id: string;
    paid: boolean;
    paidAt?: string;
    failedAt?: string;
    transactionId: string;
    paymentMethod: string;
  };
}

@Injectable()
export class SubscriptionPackageService extends TypeOrmQueryService<SubscriptionPackageEntity> {
  private readonly logger = new Logger(SubscriptionPackageService.name);

  constructor(
    @InjectRepository(SubscriptionPackageEntity)
    private readonly subscriptionPackageRepo: Repository<SubscriptionPackageEntity>
  ) {
    super(subscriptionPackageRepo, { useSoftDelete: true });
  }

  async getSubscriptionPackages(userType?: string): Promise<any> {
    // If user is admin, return all packages
    if (userType === RoleTypeEnum.Admin) {
      return await this.subscriptionPackageRepo.find();
    }

    // For regular users, only return packages marked as public
    return await this.subscriptionPackageRepo.find({
      where: { isPublic: true }
    });
  }

  /**
   * Activates a subscription for a company based on a sales order
   * @param salesOrderId The ID of the sales order
   * @param paymentMethod The payment method used (e.g., 'billplz')
   * @returns Boolean indicating success or failure
   */
  async activateSubscription(salesOrderId: number, paymentMethod: string): Promise<boolean> {
    try {
      // Get the sales order with subscription package
      const salesOrder = await getRepository(SalesOrderEntity).findOne(
        { id: salesOrderId },
        { relations: ['subscriptionPackage'] }
      );

      if (!salesOrder) {
        throw new Error(`Sales order with ID ${salesOrderId} not found`);
      }

      // Get the subscription package
      const subscriptionPackage = await this.subscriptionPackageRepo.findOne(salesOrder.subscriptionPackageId);

      if (!subscriptionPackage) {
        throw new Error(`Subscription package with ID ${salesOrder.subscriptionPackageId} not found`);
      }

      // Check for active subscription
      const activeSubscription = await getRepository(CompanySubscriptionEntity).findOne(
        {
          companyId: salesOrder.companyId,
          subscriptionEndDate: MoreThan(new Date())
        },
        {
          order: {
            subscriptionEndDate: 'DESC'
          }
        }
      );

      // Check if this is a trial conversion
      const trialSubscription = await getRepository(CompanySubscriptionEntity).findOne({
        where: {
          companyId: salesOrder.companyId,
          subscriptionPackageId: salesOrder.subscriptionPackageId,
          paymentMethod: 'Free Trial'
        }
      });

      if (trialSubscription) {
        console.log(`Found trial subscription ${trialSubscription.id} for conversion to paid subscription`);
      }

      // Parse the sales order data to get billing details
      const orderData: OrderData = salesOrder.data ? JSON.parse(salesOrder.data) as OrderData : {};

      // Calculate subscription end date
      let subscriptionEndDate: Date;
      let nextBillingDate: Date;

      // If billing details are provided in the order data, use them
      // This is the preferred path as it uses the pre-calculated billing details
      if (orderData.billingDetails && orderData.billingDetails.subscriptionEndDate) {
        console.log('Using pre-calculated billing details from sales order');

        subscriptionEndDate = new Date(orderData.billingDetails.subscriptionEndDate);

        // The next billing date should be the same as the subscription end date
        // since we've already calculated it to be the 27th of the appropriate month
        nextBillingDate = new Date(subscriptionEndDate);

        console.log(`Using pre-calculated end date: ${subscriptionEndDate.toISOString()}`);
      }
      // Otherwise, calculate based on current date and pricing logic
      else {
        // Get current date for billing calculations
        const today = new Date();
        const dayOfMonth = today.getDate();
        const isAfterMidMonth = dayOfMonth > 14;

        // Calculate the anchor date (27th of current month)
        const currentMonthAnchor = moment(today).date(27);

        // If today is after the 27th, move to next month's 27th
        if (dayOfMonth > 27) {
          currentMonthAnchor.add(1, 'month');
        }

        // Calculate the next month's anchor date (27th of next month)
        const nextMonthAnchor = moment(currentMonthAnchor).add(1, 'month');

        // Calculate the month after next anchor date (27th of month after next)
        const monthAfterNextAnchor = moment(nextMonthAnchor).add(1, 'month');

        if (activeSubscription) {
          // If there's an active subscription, extend it but ensure it aligns with the 27th
          console.log('Extending active subscription');

          // First, extend from the current subscription end date
          const billingPeriod = orderData.isYearly ? 12 : 1; // months
          const extendedEndDate = moment(activeSubscription.subscriptionEndDate).add(billingPeriod, 'months');

          // Then, ensure it aligns with the 27th anchor date
          const alignedEndDate = moment(extendedEndDate).date(27);

          // If the original date was after the 27th, move to the next month's 27th
          if (extendedEndDate.date() > 27) {
            alignedEndDate.add(1, 'month');
          }

          subscriptionEndDate = alignedEndDate.toDate();
          nextBillingDate = new Date(subscriptionEndDate.getTime());

          console.log(`Original subscription end date: ${activeSubscription.subscriptionEndDate.toISOString()}`);
          console.log(`Extended and aligned to anchor date (27th): ${subscriptionEndDate.toISOString()}`);
        }
        // For new subscriptions, apply the mid-month pricing logic
        else {
          console.log('Creating new subscription with pricing logic');

          // Determine subscription end date based on when the user is subscribing
          if (isAfterMidMonth) {
            // If after the 14th, subscription ends on the 27th of month after next
            // (prorated current period + full next month)
            subscriptionEndDate = monthAfterNextAnchor.toDate();

            // Next billing date is the same as subscription end date
            nextBillingDate = new Date(subscriptionEndDate.getTime());

            console.log(`New subscription after mid-month: End date set to ${subscriptionEndDate.toISOString()}`);
          } else {
            // If on or before the 14th, subscription ends on the 27th of next month
            subscriptionEndDate = nextMonthAnchor.toDate();

            // Next billing date is the same as subscription end date
            nextBillingDate = new Date(subscriptionEndDate.getTime());

            console.log(`New subscription before/on mid-month: End date set to ${subscriptionEndDate.toISOString()}`);
          }
        }
      }

      // Log the calculated dates for debugging
      console.log(`Final subscription end date: ${subscriptionEndDate.toISOString()}`);
      console.log(`Final next billing date: ${nextBillingDate.toISOString()}`);

      // Extract team size from order data
      const teamSize = orderData.teamSize || 1; // Default to 1 if not specified
      const isYearly = orderData.isYearly || false;

      let newSubscription: CompanySubscriptionEntity;

      // If this is a trial conversion, update the existing trial subscription
      if (trialSubscription) {
        console.log(`Converting trial subscription ${trialSubscription.id} to paid subscription`);

        await getRepository(CompanySubscriptionEntity).update(
          { id: trialSubscription.id },
          {
            paymentMethod: paymentMethod,
            subscriptionEndDate: subscriptionEndDate,
            nextBillingDate: nextBillingDate || subscriptionEndDate,
            isYearly: isYearly,
            updatedAt: new Date(),
            updatedBy: salesOrder.userId
          }
        );

        // Get the updated subscription
        newSubscription = await getRepository(CompanySubscriptionEntity).findOne(trialSubscription.id);

        console.log(`Trial subscription ${trialSubscription.id} converted to paid subscription until ${subscriptionEndDate.toISOString()}`);
      }
      // Otherwise, create a new subscription
      else {
        console.log(`Creating new subscription for company ${salesOrder.companyId}`);

        newSubscription = await getRepository(CompanySubscriptionEntity).save({
          companyId: salesOrder.companyId,
          subscriptionPackageId: salesOrder.subscriptionPackageId,
          paymentMethod: paymentMethod,
          subscriptionEndDate: subscriptionEndDate,
          nextBillingDate: nextBillingDate || subscriptionEndDate, // Use next billing date if available
          seatCount: teamSize,
          isYearly: isYearly,
          // Default values for BaseEntity fields
          createdBy: salesOrder.userId,
          updatedBy: salesOrder.userId,
          // The following fields will be automatically set by TypeORM
          // createdAt, updatedAt, recordSource
          creditBalance: 0 // Set default credit balance
        });

        console.log(`Created new subscription with ID: ${newSubscription.id}`);
      }

      // Update company maxUsers based on the subscription package and team size
      const maxUsers = Math.max(subscriptionPackage.totalUsers, teamSize);
      await getRepository(CompanyEntity).update(
        { id: salesOrder.companyId },
        { maxUsers: maxUsers }
      );

      return true;
    } catch (e) {
      getErrorMessage(e, 'SubscriptionPackageService', 'activateSubscription');
      return false;
    }
  }

  /**
   * Calculate subscription pricing using the centralized pricing helper
   * @param input Pricing calculation input parameters
   * @returns Complete pricing calculation result
   */
  async calculateSubscriptionPricing(input: PricingCalculationInputDto): Promise<PricingCalculationResultDto> {
    const calculationType = input.type || PricingCalculationType.NEW_SUBSCRIPTION;
    this.logger.log(`Calculating pricing for package ${input.subscriptionPackageId}, type: ${calculationType}`);

    try {
      // Fetch the subscription package
      const subscriptionPackage = await this.subscriptionPackageRepo.findOne({
        where: { id: parseInt(input.subscriptionPackageId) }
      });

      if (!subscriptionPackage) {
        throw new NotFoundException(`Subscription package with ID ${input.subscriptionPackageId} not found`);
      }

      let validTeamSize: number;
      let pricingResult: SubscriptionPriceResult;
      let comprehensiveResult: ComprehensivePricingCalculation;

      if (calculationType === PricingCalculationType.ADD_MEMBER) {
        // For add member calculations, calculate pricing for only the additional seats
        const currentSeatCount = input.currentSeatCount || 0;
        const additionalSeats = input.teamSize - currentSeatCount;

        if (additionalSeats <= 0) {
          throw new BadRequestException('No additional seats to calculate pricing for');
        }

        validTeamSize = additionalSeats;
        this.logger.log(`Calculating pricing for ${additionalSeats} additional seats (from ${currentSeatCount} to ${input.teamSize})`);

        // Calculate comprehensive pricing for add member
        // Note: We don't pass existingSubscriptionEndDate for add member calculations
        // because we want to calculate only until the next billing cycle (27th), not until subscription end
        comprehensiveResult = calculateComprehensivePricing(
          subscriptionPackage,
          additionalSeats,
          input.isYearly,
          input.billingDate || new Date(),
          'member_change',
          currentSeatCount,
          undefined // Don't use existing subscription end date for add member calculations
        );

        // For member changes, create a compatible pricing result from comprehensive result
        // This ensures consistent pricing calculations
        pricingResult = {
          firstPeriod: {
            startDate: new Date(comprehensiveResult.periods.currentPeriod.startDate),
            endDate: new Date(comprehensiveResult.periods.currentPeriod.endDate),
            baseAmountInCents: Math.round(comprehensiveResult.periods.currentPeriod.costs.baseAmount * 100),
            sstAmountInCents: Math.round(comprehensiveResult.periods.currentPeriod.costs.taxAmount * 100),
            totalAmountInCents: Math.round(comprehensiveResult.periods.currentPeriod.costs.totalAmount * 100),
            baseAmount: comprehensiveResult.periods.currentPeriod.costs.baseAmount,
            sstAmount: comprehensiveResult.periods.currentPeriod.costs.taxAmount,
            totalAmount: comprehensiveResult.periods.currentPeriod.costs.totalAmount,
            isProrated: comprehensiveResult.periods.currentPeriod.isProrated,
            label: comprehensiveResult.periods.currentPeriod.description,
            formattedStartDate: comprehensiveResult.periods.currentPeriod.startDate,
            formattedEndDate: comprehensiveResult.periods.currentPeriod.endDate
          },
          fullMonthPeriod: {
            startDate: new Date(comprehensiveResult.periods.nextPeriod.startDate),
            endDate: new Date(comprehensiveResult.periods.nextPeriod.endDate),
            baseAmountInCents: Math.round(comprehensiveResult.periods.nextPeriod.costs.baseAmount * 100),
            sstAmountInCents: Math.round(comprehensiveResult.periods.nextPeriod.costs.taxAmount * 100),
            totalAmountInCents: Math.round(comprehensiveResult.periods.nextPeriod.costs.totalAmount * 100),
            baseAmount: comprehensiveResult.periods.nextPeriod.costs.baseAmount,
            sstAmount: comprehensiveResult.periods.nextPeriod.costs.taxAmount,
            totalAmount: comprehensiveResult.periods.nextPeriod.costs.totalAmount,
            formattedStartDate: comprehensiveResult.periods.nextPeriod.startDate,
            formattedEndDate: comprehensiveResult.periods.nextPeriod.endDate
          },
          combined: {
            baseAmountInCents: Math.round(comprehensiveResult.periods.combinedTotals.baseAmount * 100),
            sstAmountInCents: Math.round(comprehensiveResult.periods.combinedTotals.taxAmount * 100),
            totalAmountInCents: Math.round(comprehensiveResult.periods.combinedTotals.totalAmount * 100),
            baseAmount: comprehensiveResult.periods.combinedTotals.baseAmount,
            sstAmount: comprehensiveResult.periods.combinedTotals.taxAmount,
            totalAmount: comprehensiveResult.periods.combinedTotals.totalAmount
          },
          subscriptionEndDate: new Date(comprehensiveResult.paymentSchedule.subscriptionEndDate),
          formattedSubscriptionEndDate: comprehensiveResult.paymentSchedule.subscriptionEndDate,
          isFirstDayOfMonth: false, // Not relevant for member changes
          isAfterMidMonth: true, // Member changes are always considered after mid-month for proration
          dayOfMonth: new Date(input.billingDate || new Date()).getDate()
        };
      } else {
        // For new subscription calculations, use the full team size
        validTeamSize = Math.max(1, input.teamSize || 1);
        if (validTeamSize !== input.teamSize) {
          this.logger.warn(`Team size adjusted from ${input.teamSize} to ${validTeamSize}`);
        }

        // Use the existing pricing calculation logic
        pricingResult = calculateSubscriptionPrice(
          subscriptionPackage,
          validTeamSize,
          input.isYearly,
          input.billingDate || new Date()
        );

        // Calculate comprehensive pricing for new subscription
        comprehensiveResult = calculateComprehensivePricing(
          subscriptionPackage,
          validTeamSize,
          input.isYearly,
          input.billingDate || new Date(),
          'new'
        );
      }

      // Transform the result to match our GraphQL schema
      const result: PricingCalculationResultDto = {
        firstPeriod: {
          startDate: pricingResult.firstPeriod.startDate,
          endDate: pricingResult.firstPeriod.endDate,
          baseAmountInCents: pricingResult.firstPeriod.baseAmountInCents,
          sstAmountInCents: pricingResult.firstPeriod.sstAmountInCents,
          totalAmountInCents: pricingResult.firstPeriod.totalAmountInCents,
          baseAmount: pricingResult.firstPeriod.baseAmount,
          sstAmount: pricingResult.firstPeriod.sstAmount,
          totalAmount: pricingResult.firstPeriod.totalAmount,
          isProrated: pricingResult.firstPeriod.isProrated,
          label: pricingResult.firstPeriod.label,
          formattedStartDate: pricingResult.firstPeriod.formattedStartDate,
          formattedEndDate: pricingResult.firstPeriod.formattedEndDate
        },
        fullMonthPeriod: {
          startDate: pricingResult.fullMonthPeriod.startDate,
          endDate: pricingResult.fullMonthPeriod.endDate,
          baseAmountInCents: pricingResult.fullMonthPeriod.baseAmountInCents,
          sstAmountInCents: pricingResult.fullMonthPeriod.sstAmountInCents,
          totalAmountInCents: pricingResult.fullMonthPeriod.totalAmountInCents,
          baseAmount: pricingResult.fullMonthPeriod.baseAmount,
          sstAmount: pricingResult.fullMonthPeriod.sstAmount,
          totalAmount: pricingResult.fullMonthPeriod.totalAmount,
          formattedStartDate: pricingResult.fullMonthPeriod.formattedStartDate,
          formattedEndDate: pricingResult.fullMonthPeriod.formattedEndDate
        },
        combined: {
          baseAmountInCents: pricingResult.combined.baseAmountInCents,
          sstAmountInCents: pricingResult.combined.sstAmountInCents,
          totalAmountInCents: pricingResult.combined.totalAmountInCents,
          baseAmount: pricingResult.combined.baseAmount,
          sstAmount: pricingResult.combined.sstAmount,
          totalAmount: pricingResult.combined.totalAmount
        },
        subscriptionEndDate: pricingResult.subscriptionEndDate,
        formattedSubscriptionEndDate: pricingResult.formattedSubscriptionEndDate,
        isFirstDayOfMonth: pricingResult.isFirstDayOfMonth,
        isAfterMidMonth: pricingResult.isAfterMidMonth,
        dayOfMonth: pricingResult.dayOfMonth,
        teamSize: validTeamSize,
        isYearly: input.isYearly,
        monthlyPricePerMember: subscriptionPackage.amount,
        discountedMonthlyPricePerMember: input.isYearly
          ? Math.round(subscriptionPackage.amount * (1 - YEARLY_DISCOUNT_RATE))
          : subscriptionPackage.amount,
        yearlyDiscountRate: input.isYearly ? YEARLY_DISCOUNT_RATE : 0,
        pricingDetails: this.buildPricingDetails(
          pricingResult,
          subscriptionPackage,
          input,
          calculationType,
          validTeamSize
        ),
        // Comprehensive pricing fields (merged)
        subscription: {
          packageId: comprehensiveResult.subscription.packageId,
          packageName: comprehensiveResult.subscription.packageName,
          isYearly: comprehensiveResult.subscription.isYearly,
          calculationType: comprehensiveResult.subscription.calculationType
        },
        memberPricing: {
          originalPricePerMember: comprehensiveResult.memberPricing.originalPricePerMember,
          discountedPricePerMember: comprehensiveResult.memberPricing.discountedPricePerMember,
          effectiveMonthlyPricePerMember: comprehensiveResult.memberPricing.effectiveMonthlyPricePerMember,
          currentTeamSize: comprehensiveResult.memberPricing.currentTeamSize,
          newTeamSize: comprehensiveResult.memberPricing.newTeamSize
        },
        periods: {
          currentPeriod: {
            startDate: comprehensiveResult.periods.currentPeriod.startDate,
            endDate: comprehensiveResult.periods.currentPeriod.endDate,
            description: comprehensiveResult.periods.currentPeriod.description,
            isProrated: comprehensiveResult.periods.currentPeriod.isProrated,
            costs: {
              baseAmount: comprehensiveResult.periods.currentPeriod.costs.baseAmount,
              discountAmount: comprehensiveResult.periods.currentPeriod.costs.discountAmount,
              subtotalAmount: comprehensiveResult.periods.currentPeriod.costs.subtotalAmount,
              taxAmount: comprehensiveResult.periods.currentPeriod.costs.taxAmount,
              totalAmount: comprehensiveResult.periods.currentPeriod.costs.totalAmount,
              taxRate: comprehensiveResult.periods.currentPeriod.costs.taxRate,
              discountRate: comprehensiveResult.periods.currentPeriod.costs.discountRate
            }
          },
          nextPeriod: {
            startDate: comprehensiveResult.periods.nextPeriod.startDate,
            endDate: comprehensiveResult.periods.nextPeriod.endDate,
            description: comprehensiveResult.periods.nextPeriod.description,
            isProrated: comprehensiveResult.periods.nextPeriod.isProrated,
            costs: {
              baseAmount: comprehensiveResult.periods.nextPeriod.costs.baseAmount,
              discountAmount: comprehensiveResult.periods.nextPeriod.costs.discountAmount,
              subtotalAmount: comprehensiveResult.periods.nextPeriod.costs.subtotalAmount,
              taxAmount: comprehensiveResult.periods.nextPeriod.costs.taxAmount,
              totalAmount: comprehensiveResult.periods.nextPeriod.costs.totalAmount,
              taxRate: comprehensiveResult.periods.nextPeriod.costs.taxRate,
              discountRate: comprehensiveResult.periods.nextPeriod.costs.discountRate
            }
          },
          combinedTotals: {
            baseAmount: comprehensiveResult.periods.combinedTotals.baseAmount,
            discountAmount: comprehensiveResult.periods.combinedTotals.discountAmount,
            subtotalAmount: comprehensiveResult.periods.combinedTotals.subtotalAmount,
            taxAmount: comprehensiveResult.periods.combinedTotals.taxAmount,
            totalAmount: comprehensiveResult.periods.combinedTotals.totalAmount,
            taxRate: comprehensiveResult.periods.combinedTotals.taxRate,
            discountRate: comprehensiveResult.periods.combinedTotals.discountRate
          }
        },
        savings: comprehensiveResult.savings ? {
          monthlyBillingTotal: comprehensiveResult.savings.monthlyBillingTotal,
          yearlyBillingTotal: comprehensiveResult.savings.yearlyBillingTotal,
          annualSavings: comprehensiveResult.savings.annualSavings,
          savingsPercentage: comprehensiveResult.savings.savingsPercentage,
          monthlySavings: comprehensiveResult.savings.monthlySavings
        } : undefined,
        paymentSchedule: {
          nextPaymentDate: comprehensiveResult.paymentSchedule.nextPaymentDate,
          nextPaymentAmount: comprehensiveResult.paymentSchedule.nextPaymentAmount,
          billingFrequency: comprehensiveResult.paymentSchedule.billingFrequency,
          subscriptionEndDate: comprehensiveResult.paymentSchedule.subscriptionEndDate,
          daysUntilNextPayment: comprehensiveResult.paymentSchedule.daysUntilNextPayment,
          ongoingMonthlyAmount: comprehensiveResult.paymentSchedule.ongoingMonthlyAmount,
          previousMonthlyAmount: comprehensiveResult.paymentSchedule.previousMonthlyAmount
        },
        context: {
          currency: comprehensiveResult.context.currency,
          timezone: comprehensiveResult.context.timezone,
          calculatedAt: comprehensiveResult.context.calculatedAt
        }
      };

      this.logger.log(`Pricing calculation completed. Total: RM ${result.combined.totalAmount}`);

      return result;
    } catch (error) {
      this.logger.error(`Error in pricing calculation: ${error.message}`, error.stack);

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException(`Pricing calculation failed: ${error.message}`);
    }
  }

  /**
   * Get subscription package by ID (helper method)
   * @param packageId Subscription package ID
   * @returns Subscription package entity
   */
  async getSubscriptionPackageById(packageId: string): Promise<SubscriptionPackageEntity> {
    const subscriptionPackage = await this.subscriptionPackageRepo.findOne({
      where: { id: parseInt(packageId) }
    });

    if (!subscriptionPackage) {
      throw new NotFoundException(`Subscription package with ID ${packageId} not found`);
    }

    return subscriptionPackage;
  }

  /**
   * Build detailed pricing information for frontend consumption
   * @param pricingResult The pricing calculation result from the helper
   * @param subscriptionPackage The subscription package entity
   * @param input The original input parameters
   * @param calculationType The type of calculation being performed
   * @param validTeamSize The validated team size used in calculations
   * @returns Detailed pricing information
   */
  private buildPricingDetails(
    pricingResult: SubscriptionPriceResult,
    subscriptionPackage: SubscriptionPackageEntity,
    input: PricingCalculationInputDto,
    calculationType: PricingCalculationType,
    validTeamSize: number
  ): PricingDetailsDto {
    // Calculate the effective price per member (with yearly discount if applicable)
    const pricePerMember = input.isYearly
      ? Math.round(subscriptionPackage.amount * (1 - YEARLY_DISCOUNT_RATE))
      : subscriptionPackage.amount;

    // Build current period prorated details
    const currentPeriodProrated: CurrentPeriodProratedDto = {
      amount: pricingResult.firstPeriod.totalAmount,
      startDate: pricingResult.firstPeriod.formattedStartDate,
      endDate: pricingResult.firstPeriod.formattedEndDate
    };

    // Build next period details
    const nextPeriod: NextPeriodDto = {
      amount: pricingResult.fullMonthPeriod.totalAmount,
      startDate: pricingResult.fullMonthPeriod.formattedStartDate,
      endDate: pricingResult.fullMonthPeriod.formattedEndDate
    };

    // Build yearly savings details (only for yearly subscriptions)
    let yearlySavings: YearlySavingsDto | undefined;
    if (input.isYearly) {
      const monthlyTotal = subscriptionPackage.amount * validTeamSize * 12;
      const yearlyTotal = pricePerMember * validTeamSize * 12;
      const amountSaved = monthlyTotal - yearlyTotal;

      yearlySavings = {
        percentage: YEARLY_DISCOUNT_RATE * 100, // Convert to percentage
        amountSaved: amountSaved
      };
    }

    // Build pricing breakdown
    const breakdown: PricingBreakdownDto = {
      basePrice: pricePerMember,
      memberCount: calculationType === PricingCalculationType.ADD_MEMBER
        ? validTeamSize // For add member, this is the additional seats count
        : validTeamSize, // For new subscription, this is the total team size
      subtotal: pricingResult.combined.baseAmount,
      sst: pricingResult.combined.sstAmount,
      total: pricingResult.combined.totalAmount
    };

    return {
      pricePerMember,
      currentPeriodProrated,
      nextPeriod,
      yearlySavings,
      breakdown
    };
  }


}
