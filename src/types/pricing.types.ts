/**
 * Enhanced Pricing Types for Subscription Management
 *
 * This file defines comprehensive types for pricing calculations that support:
 * - Subscription confirmations
 * - Member management
 * - Billing period comparisons
 * - Invoice generation
 * - Pricing breakdowns
 */

/**
 * Basic billing period information
 */
export interface BillingPeriod {
  /** Start date of the billing period (formatted) */
  startDate: string;
  /** End date of the billing period (formatted) */
  endDate: string;
  /** Duration description (e.g., "Partial Month", "Full Month", "Annual") */
  description: string;
  /** Whether this period is prorated */
  isProrated: boolean;
}

/**
 * Detailed cost breakdown for a billing period
 */
export interface PeriodCostBreakdown {
  /** Base amount before taxes and discounts */
  baseAmount: number;
  /** Discount amount (if any) */
  discountAmount: number;
  /** Amount after discount, before tax */
  subtotalAmount: number;
  /** Tax amount (SST) */
  taxAmount: number;
  /** Final total amount */
  totalAmount: number;
  /** Tax rate applied (e.g., 0.06 for 6% SST) */
  taxRate: number;
  /** Discount rate applied (e.g., 0.10 for 10% discount) */
  discountRate: number;
}

/**
 * Combined billing period with costs
 */
export interface PricingPeriod extends BillingPeriod {
  /** Detailed cost breakdown for this period */
  costs: PeriodCostBreakdown;
}

/**
 * Per-member pricing information
 */
export interface MemberPricing {
  /** Original price per member per month */
  originalPricePerMember: number;
  /** Discounted price per member per month (after yearly discount) */
  discountedPricePerMember: number;
  /** Effective monthly price per member (considering billing frequency) */
  effectiveMonthlyPricePerMember: number;
  /** Current team size */
  currentTeamSize: number;
  /** New team size (if changing membership) */
  newTeamSize?: number;
}

/**
 * Savings information for yearly subscriptions
 */
export interface SavingsInfo {
  /** Monthly cost if billed monthly */
  monthlyBillingTotal: number;
  /** Yearly cost if billed yearly */
  yearlyBillingTotal: number;
  /** Amount saved per year */
  annualSavings: number;
  /** Percentage saved */
  savingsPercentage: number;
  /** Amount saved per month */
  monthlySavings: number;
}

/**
 * Payment schedule information
 */
export interface PaymentSchedule {
  /** Next payment date */
  nextPaymentDate: string;
  /** Next payment amount */
  nextPaymentAmount: number;
  /** Billing frequency (monthly/yearly) */
  billingFrequency: 'monthly' | 'yearly';
  /** Subscription end date */
  subscriptionEndDate: string;
  /** Days until next payment */
  daysUntilNextPayment: number;
  /** Ongoing monthly subscription amount (for member changes) */
  ongoingMonthlyAmount?: number;
  /** Previous monthly amount (before member change) */
  previousMonthlyAmount?: number;
}

/**
 * Comprehensive pricing calculation result
 */
export interface ComprehensivePricingCalculation {
  /** Basic subscription information */
  subscription: {
    /** Subscription package ID */
    packageId: string;
    /** Package name */
    packageName: string;
    /** Whether this is a yearly subscription */
    isYearly: boolean;
    /** Subscription type (new, upgrade, member_change, etc.) */
    calculationType: 'new' | 'upgrade' | 'member_change' | 'renewal';
  };

  /** Per-member pricing details */
  memberPricing: MemberPricing;

  /** Billing periods breakdown */
  periods: {
    /** Current/First billing period (may be prorated) */
    currentPeriod: PricingPeriod;
    /** Next/Ongoing billing period (full period) */
    nextPeriod: PricingPeriod;
    /** Combined totals for display purposes */
    combinedTotals: PeriodCostBreakdown;
  };

  /** Savings information (for yearly subscriptions) */
  savings?: SavingsInfo;

  /** Payment schedule */
  paymentSchedule: PaymentSchedule;

  /** Additional context */
  context: {
    /** Currency code */
    currency: string;
    /** Timezone for date calculations */
    timezone: string;
    /** Calculation timestamp */
    calculatedAt: string;
  };
}

/**
 * Simplified pricing display data (for UI components that need less detail)
 */
export interface SimplifiedPricingDisplay {
  /** Display price per member */
  pricePerMember: string;
  /** Display total monthly amount */
  totalMonthly: string;
  /** Display total amount for billing period */
  totalForPeriod: string;
  /** Renewal date */
  renewalDate: string;
  /** Savings text (if applicable) */
  savingsText?: string;
  /** Whether this includes prorated amounts */
  includesProration: boolean;
}
